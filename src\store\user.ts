import type {
  GetUserServerRes,
  LoginParams,

  LoginRes,
} from '@/service/systemApi'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  getUserServerInfoApi,
  wxParentsUserLoginPath,
} from '@/service/systemApi'

export const useUserStore = defineStore(
  'user',
  () => {
    // 黑名单
    const inBlacklist = ref(false)
    // 厂商唯一识别，用于记录黑名单
    const dataCode = ref('')
    const expiration = 30 * 60 * 1000 // 过期时间为30分钟
    const userId = ref<number>(0)
    const userCode = ref('')
    const realName = ref('')
    const authorization = ref('')
    const downloadEmail = ref('')
    const defaultInvoiceTempId = ref(0)
    const timeout = ref(0)
    const phone = ref('')
    const barCodeCardNum = ref('')
    const barCodeCardPassword = ref('')
    // 信息通报权限
    const reportAuth = ref(false)
    // 注册权限
    const registerServiceAuth = ref(false)
    // 是否有未完成的业务
    const existUncompletedRegister = ref(false)
    // 续展权限
    const renewalServiceAuth = ref(false)
    // 是否有未完成的业务
    const existUncompletedRenewal = ref(false)
    // 变更权限
    const modifyServiceAuth = ref(false)
    // 是否有未完成的业务
    const existUncompletedChange = ref(false)
    // TODO 账号密码是否错了，true:有错，false:没错
    const isAccountError = ref(true)
    // 什么时候更新了账号密码,24小时只提示一次
    const updatePasswordTime = ref(0)

    const setUpdatePasswordTime = () => {
      // 设置当前时间搓
      updatePasswordTime.value = Date.now()
    }

    const isShowIncorrectPasswordTip = () => {
      // 如果没错，则不需要提示
      if (!isAccountError.value)
        return false

      // 如果没有记录更新密码的时间，则显示提示
      if (updatePasswordTime.value === 0) {
        return true
      }

      // 计算当前时间与更新密码时间的差值（毫秒）
      const timeDiff = Date.now() - updatePasswordTime.value

      // 24小时 = 24 * 60 * 60 * 1000 毫秒
      const twentyFourHours = 24 * 60 * 60 * 1000

      // 如果超过24小时，则显示提示
      return timeDiff >= twentyFourHours
    }

    const login = (companyCode?: string) =>
      new Promise((resolve, reject) => {
        // 过期及快过期才重新登录
        if (Date.now() > timeout.value) {
          uni.login({
            success(res) {
              console.log(res)
              const data: LoginParams = {
                code: res.code,
              }
              let url = ''
              data.fromTo = import.meta.env.VITE_FROM_PLATFORM
              url = wxParentsUserLoginPath
              if (companyCode) {
                data.dataCode = companyCode
              }
              uni.request({
                url,
                method: 'POST',
                data,
                success: (res: any) => {
                  const dataRes = res.data as LoginRes
                  if (dataRes.success) {
                    const d = dataRes.data
                    // inBlacklist.value = true
                    inBlacklist.value = d.inBlacklist
                    userId.value = Number(d.userId)
                    userCode.value = d.userCode
                    realName.value = d.realName
                    authorization.value = d.authorization
                    timeout.value = Date.now() + expiration
                    resolve(res)
                  }
                  else {
                    reject(res)
                  }
                },
                fail: (err) => {
                  reject(err)
                },
              })
            },
            fail(err) {
              reject(err)
            },
          })
        }
        else {
          resolve(true)
        }
      })

    const getUserServerInfo = () =>
      new Promise<GetUserServerRes>((resolve, reject) => {
        getUserServerInfoApi({
          // certificationId: 0,
          userId: userId.value,
        })
          .then((res) => {
            // console.log('existUncompletedRegister', res.data.existUncompletedRegister)
            // console.log('existUncompletedRenewal', res.data.existUncompletedRenewal)
            // console.log('existUncompletedChange', res.data.existUncompletedChange)
            reportAuth.value = res.data.reportAuth
            registerServiceAuth.value = res.data.registerAuth
            renewalServiceAuth.value = res.data.renewalAuth
            modifyServiceAuth.value = res.data.changeAuth
            dataCode.value = res.data.dataCode
            existUncompletedRegister.value = res.data.existUncompletedRegister
            existUncompletedRenewal.value = res.data.existUncompletedRenewal
            existUncompletedChange.value = res.data.existUncompletedChange
            phone.value = res.data.phone
            barCodeCardNum.value = res.data.barCodeCardNum
            barCodeCardPassword.value = res.data.barCodeCardPassword
            isAccountError.value = res.data.isAccountError
            resolve(res)
          })
          .catch((err) => {
            reject(err)
          })
      })

    return {
      inBlacklist,
      dataCode,
      userId,
      userCode,
      realName,
      authorization,
      downloadEmail,
      defaultInvoiceTempId,
      phone,
      barCodeCardNum,
      barCodeCardPassword,
      reportAuth,
      registerServiceAuth,
      renewalServiceAuth,
      modifyServiceAuth,
      existUncompletedRegister,
      existUncompletedRenewal,
      existUncompletedChange,
      isAccountError,
      updatePasswordTime,
      setUpdatePasswordTime,
      isShowIncorrectPasswordTip,
      login,
      getUserServerInfo,
    }
  },
  {
    persist: true,
  },
)
