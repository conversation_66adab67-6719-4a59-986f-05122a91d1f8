<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "商品通报管理"
  }
}
</route>

<script lang="ts" setup>
import type { ReportGoodsPageRes, ReportOverviewRes } from '@/service/infoReportApi'
import type { CheckGroupChangeType } from '@/types/myType'
import debounce from 'debounce'
import { DesignImage } from '@/components/image'
import { Color } from '@/enums/colorEnum'
import { OrderDirection } from '@/enums/httpEnum'
import { getSyncAddStateStr, getSyncWxStateStr, SyncAddState, SyncWxState } from '@/enums/index'
import { reportGoodsPageApi, reportOverviewApi } from '@/service/infoReportApi'

import { useUserStore } from '@/store/user'

const PAGE_SIZE = 10

const keyword = ref('')
const isShowFilter = ref(false)
const isFilter = ref(false)
const isShowInfoBar = ref(false)
const productList = ref<ReportGoodsPageRes['data']>([])
const refreshTriggered = ref(false)
const loading = ref(false)
const overviewLoading = ref(false)
const hasMore = ref(true)
const pageIndex = ref(1)
const overviewData = ref<ReportOverviewRes['data']>()
const syncAddStateArr = ref<SyncAddState[]>([SyncAddState.success, SyncAddState.processing, SyncAddState.failed, SyncAddState.new])

enum Status {
  fail = 0,
  done = 1,
  processing = 2,
}

function getReportGoodsList() {
  loading.value = true
  reportGoodsPageApi({
    // barCode: '',
    // brandName:'',
    // certificationId:
    // endDate:
    // goodsName:
    // groupBy:
    needTotalCount: true,
    orderBy: '',
    orderDirection: OrderDirection.desc,
    pageIndex: pageIndex.value,
    pageSize: PAGE_SIZE,
    // startDate:
    userId: useUserStore().userId,
  }).then((res) => {
    if (pageIndex.value === 1) {
      productList.value = res.data
    }
    else {
      productList.value = [...productList.value, ...res.data]
    }

    // 判断是否还有更多数据
    hasMore.value = res.data.length === PAGE_SIZE

    if (hasMore.value) {
      pageIndex.value++
    }
  }).finally(() => {
    loading.value = false
    refreshTriggered.value = false
  })
}

const handleInput = debounce(() => {
  handleSearch()
}, 800)

function handleSearch() {
  console.log(keyword.value)
}

function getOverview() {
  overviewLoading.value = true
  reportOverviewApi(useUserStore().userId).then((res) => {
    overviewData.value = res.data
  }).finally(() => {
    overviewLoading.value = false
  })
}

function getStatusStrAndColor(data: ReportGoodsPageRes['data'][0]) {
  const addStr = getSyncAddStateStr(data.syncAddState)
  const wxStr = getSyncWxStateStr(data.syncState)
  switch (data.syncAddState) {
    case SyncAddState.failed:
      return [addStr, 'text-red-500']
    case SyncAddState.success:
      // 再判断微信共享情况
      switch (data.syncState) {
        case SyncWxState.failed:
          return [wxStr, 'text-red-500']
        case SyncWxState.success:
          return [addStr, 'text-emerald-500']
        case SyncWxState.processing:
          return [wxStr, 'text-primary']
        default:
          return [wxStr, 'text-gray-500']
      }
    case SyncAddState.processing:
      return [addStr, 'text-primary']
    default:
      return [addStr, 'text-gray-500']
  }
}
function onRefresh() {
  if (refreshTriggered.value)
    return
  pageIndex.value = 1
  refreshTriggered.value = true
  getReportGoodsList()
  getOverview()
}

function showDetail(goodsId: number) {
  uni.navigateTo({
    url: `/pages/infoReportMg/infoReportDetailPage?goodsId=${goodsId}`,
  })
}

function checkboxChange(e?: CheckGroupChangeType) {
  if (e?.detail?.value) {
    // syncAddStateArr.value = e.detail.value
    syncAddStateArr.value = e?.detail?.value.map((item) => {
      return Number(item)
    })
    getReportGoodsList()
  }
}

// TODO overviewData未匹配
// TODO 密码错误标记，可直接更新账号密码

// 后退刷新
onShow(() => {
  getReportGoodsList()
  getOverview()
})
</script>

<template>
  <view>
    <view class="f-search-bar flex items-center bg-white px-3 shadow-blue">
      <view class="o-bg-no flex flex-1 items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-icon :color="Color.gray" name="search" size="20" />
        <up-input
          v-model="keyword" :maxlength="14" border="none" class="o-bg-transparent grow" clearable
          placeholder="条码/商品名称" @change="handleInput"
        />
        <view
          :class="isFilter ? 'text-primary' : 'o-color-aid'" class="flex items-center gap-1 text-sm"
          @click="isShowFilter = !isShowFilter"
        >
          <view class="shrink-0">
            筛选/排序
          </view>
          <view style="min-width: 1.5rem">
            <up-icon v-if="isShowFilter" name="arrow-up" size="14" />
            <up-icon v-else name="arrow-down" size="14" />
          </view>
        </view>
      </view>
    </view>
    <view class="relative">
      <view v-if="isShowFilter" class="absolute left-0 z-10 box-border w-full bg-white p-4 shadow-lg">
        <checkbox-group class="flex flex-wrap gap-2" @change="checkboxChange">
          <label class="mr-2 flex items-center">
            <checkbox
              :color="Color.primary" style="transform: scale(0.7)" :value="`${SyncAddState.success}`"
              :checked="syncAddStateArr.includes(SyncAddState.success)"
            />
            <text class="text-emerald-500">已通报</text>
          </label>
          <label class="flex items-center">
            <checkbox
              :color="Color.primary" style="transform: scale(0.7)" :value="`${SyncAddState.processing}`"
              :checked="syncAddStateArr.includes(SyncAddState.processing)"
            />
            <text class="text-primary">通报中</text>
          </label>
          <label class="mr-2 flex items-center">
            <checkbox
              :color="Color.primary" style="transform: scale(0.7)" :value="`${SyncAddState.failed}`"
              :checked="syncAddStateArr.includes(SyncAddState.failed)"
            />
            <text class="text-red-500">通报失败</text>
          </label>
          <label class="flex items-center">
            <checkbox
              :color="Color.primary" style="transform: scale(0.7)" :value="`${SyncAddState.new}`"
              :checked="syncAddStateArr.includes(SyncAddState.new)"
            />
            <text>未通报</text>
          </label>
        </checkbox-group>
      </view>
    </view>
    <scroll-view
      :scroll-y="true" :refresher-enabled="true" :refresher-triggered="refreshTriggered"
      :scroll-with-animation="true" class="o-bg-no f-scroll-view" @refresherrefresh="onRefresh"
    >
      <view class="p-3 space-y-2">
        <view
          v-for="item in productList" :key="item.goodsId" class="rounded bg-white p-3"
          @click="showDetail(item.goodsId)"
        >
          <view class="flex items-baseline justify-between text-sm leading-4">
            <view class="text-gray">
              {{ item.barCode }}
            </view>
            <view :class="getStatusStrAndColor(item)[1]">
              {{ getStatusStrAndColor(item)[0] }}
            </view>
          </view>
          <view class="mt-1 flex items-baseline justify-between leading-4 space-x-4">
            <view class="line-clamp-2 flex-1 font-bold">
              {{ item.goodsName }}
            </view>
            <view class="line-clamp-2 shrink-0 text-sm text-gray" style="max-width: 30vw">
              {{ item.brandName }}
            </view>
          </view>
          <view class="mt-1 flex items-baseline justify-between text-sm text-gray leading-4">
            <view class="line-clamp-2" style="max-width: 40vw">
              净含量：{{ item.netContent }}
            </view>
            <view class="line-clamp-2" style="max-width: 30vw">
              规格：{{ item.spec }}
            </view>
          </view>
          <view
            v-if="item.syncAddState === SyncAddState.failed || item.syncAddState === SyncAddState.processing"
            class="o-line my-2"
          />
          <view v-if="item.syncAddMsg && item.syncAddMsg !== ''" class="text-sm text-red-500">
            {{ item.syncAddMsg }}
          </view>
          <view v-if="item.syncMsg && item.syncMsg !== ''" class="text-sm text-red-500">
            {{ item.syncMsg }}
          </view>
          <view class="mt-2 flex justify-end space-x-4">
            <!-- TODO 跟更正密码跟重试合成一个，更新一次后 -->
            <view
              v-if="true"
              class="rounded bg-gray-100 px-4 py-2 text-red-500"
            >
              更正账号密码
            </view>
            <view v-if="item.syncAddState === SyncAddState.failed" class="rounded bg-red-50 px-4 py-2 text-red-500">
              重试
            </view>
          </view>
        </view>
      </view>
      <view v-if="loading" class="flex justify-center py-4">
        <up-loading-icon mode="semicircle" />
        <text class="ml-2 text-gray-500">加载中...</text>
      </view>
      <view v-else-if="!hasMore" class="flex justify-center py-8">
        <text class="text-gray-400">没有更多数据了</text>
      </view>
      <view class="o-pb" />
    </scroll-view>
    <view
      class="pointer-events-none fixed right-0 box-border w-full p-3"
      style="bottom:calc(env(safe-area-inset-bottom) + 10rpx)"
    >
      <view
        v-if="!isShowInfoBar"
        class="o-tabbar-shadow pointer-events-auto float-right w-fit flex items-center rounded-full bg-white py-1 pl-1 pr-5 space-x-1"
        @click="isShowInfoBar = true"
      >
        <view v-if="overviewLoading" class="flex items-center justify-center px-6 py-3">
          <up-loading-icon mode="semicircle" />
        </view>
        <template v-else>
          <up-icon name="checkmark-circle-fill" size="50" :color="Color.emerald" />
          <view>
            <view class="text-sm">
              已完成所有通报
            </view>
            <view class="text-2xs text-gray">
              点击查看概况
            </view>
          </view>
        </template>
      </view>
      <view
        v-if="isShowInfoBar"
        class="o-tabbar-shadow f-Info-box pointer-events-auto box-border w-full rounded-lg bg-white p-4"
      >
        <view class="mb-2 text-xl font-bold">
          产品通报概况
        </view>
        <up-skeleton rows="2" :loading="overviewLoading">
          <view>
            <!-- <view class="flex items-center justify-between text-gray">
              <view class="shrink-0">
                条码容量占用情况：
              </view>
              <view class="flex">
                <view>已使用：21</view>
                <view>
                  <text class="px-1">
                    /
                  </text>总量：10000
                </view>
              </view>
            </view>
            <up-line-progress class="mt-2" :percentage="30" :active-color="Color.primary" /> -->

            <view class="grid grid-cols-4 text-sm">
              <view class="flex flex-col items-center text-emerald-500">
                <view>已通报</view>
                <view class="text-lg font-bold">
                  {{ overviewData?.unReportNum }}
                </view>
              </view>
              <view
                class="flex flex-col items-center border-l border-r border-l-gray-200 border-r-gray-200 border-l-solid border-r-solid text-primary"
              >
                <view>通报中</view>
                <view class="text-lg font-bold">
                  {{ overviewData?.unReportNum }}
                </view>
              </view>
              <view class="flex flex-col items-center border-r border-r-gray-200 border-r-solid text-red-500">
                <view>通报失败</view>
                <view class="text-lg font-bold">
                  {{ overviewData?.reportNum }}
                </view>
              </view>
              <view class="flex flex-col items-center">
                <view>产品总数</view>
                <view class="text-lg font-bold">
                  {{ overviewData?.reportNum }}
                </view>
              </view>
            </view>
            <!-- <view class="mt-4 flex items-center justify-between space-x-2">
              <view class="center flex-1 rounded bg-gray-100 py-2 text-amber-500">
                一键取消正在通报
              </view>
              <view class="center flex-1 rounded bg-red-50 py-2 text-red-500">
                一键重试失败通报
              </view>
            </view> -->
          </view>
        </up-skeleton>
        <view class="mt-4 flex items-end text-sm space-x-2">
          <view class="f-big-btn o-btn-light-bg-blue-border o-shadow-blue-light center flex-1 flex-col">
            <image :src="DesignImage.btnIcon.cameras" mode="widthFix" class="f-btn-icon-md" />
            <text class="f-btn-text">
              拍照自主通报
            </text>
          </view>
          <view class="f-big-btn o-btn-light-bg-blue-border o-shadow-blue-light center flex-1 flex-col">
            <image :src="DesignImage.btnIcon.file" mode="widthFix" class="f-btn-icon-md" />
            <text class="f-btn-text">
              批量代办通报
            </text>
          </view>
          <view
            class="f-c-btn center shrink-0 rounded-full bg-primary text-xs text-white"
            @click="isShowInfoBar = false"
          >
            收起
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
$h: 96rpx;

.f-search-bar {
  height: $h;
}

.f-scroll-view {
  height: calc(100vh - $h);
}

.f-Info-box {
  border-radius: 6px 6px 36px 6px;
}

.f-big-btn {
  height: 146rpx;
}

.f-btn-icon-md {
  width: 106rpx;
}

.f-c-btn {
  $w: 74rpx;
  height: $w;
  width: $w;
}
</style>
