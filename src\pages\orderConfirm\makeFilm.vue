<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "订单确认"
  }
}
</route>

<script lang="ts" setup>
import type {
  CouponPageResData,
  PriceTempListResData,
  SubmitOrderParams,
} from '@/service/orderApi'
import debounce from 'debounce'
import { storeToRefs } from 'pinia'
import PaidDisclaimer from '@/components/agreement/paidDisclaimer.vue'
import { DESCRIPTION_STR, makeFilmAndReportServiceDescription } from '@/components/descriptionStr'
import { wxRequestPayment } from '@/components/mix'
import DiscountBox from '@/components/Price/DiscountBox.vue'
import PriceBox from '@/components/Price/PriceBox.vue'
import { Article, CouponType, couponTypeStr, ServerType } from '@/enums'
import {
  payOrderApi,
  priceTempListApi,
  submitOrderApi,
} from '@/service/orderApi'
import { useCouponStore } from '@/store/couponStore'
import { msgModalStore } from '@/store/msgModalStore'
import { orderStore } from '@/store/orderStore'
import { useServiceStore } from '@/store/serviceStore'
import { catchErrorAndNavigateBack } from '@/utils'

const useMsgModalStore = msgModalStore()

const serviceStore = useServiceStore()
const { descriptionServiceType, isHasOtherServer, barCodePrice, extraPrice, isFirstCode }
  = storeToRefs(serviceStore)
const useOrderStore = orderStore()
const {
  couponId,
  orderCode,
  serverType,
  startBarCode,
  endBarCode,
  size,
  price,
  useCount,
  total,
  existCount,
  couponPrice,
  discount,
  totalPrice,
  actuallyPrice,
  discountsPrice,
  couponType,
  isSelectedCoupon,
  vendorCode,
} = storeToRefs(useOrderStore)
// TODO 是否正常带过来
const { couponList, bestDiscountItem } = storeToRefs(useCouponStore)

const isShowStep = ref(true)
const btnDisable = ref(false)
const btnStr = ref('马上生成')
const agree = ref(false)
const isModalShow = ref(false)
const serveData = ref<PriceTempListResData[]>([])
const infoReportPrice = ref(0)

const articleIds = computed(() => {
  if (isHasOtherServer.value) {
    return [Article.film, Article.infoReport, Article.platform]
  }
  else {
    return [Article.film, Article.platform]
  }
})

function getTempList() {
  priceTempListApi({
    tempType: serverType.value,
    isDefault: isFirstCode.value ? 1 : 0,
  }).then((res) => {
    serveData.value = res.data
  })
}

function getInfoReportPriceTemp() {
  priceTempListApi({ tempType: ServerType.infoReport }).then((res: any) => {
    infoReportPrice.value = res.data[0].price
  })
}

/**
 * 找出面值最大的，最低使用额度满足的代金券
 * @param list
 */
function findMaxPriceCoupon(list: any[]) {
  // 过滤出符合条件的对象，无需判断优惠券类型serverType，因为请求的是serverType===1的数据
  const filteredList = list.filter(
    item =>
      item.couponType === CouponType.cash
      && item.vendorCode === vendorCode.value
      && item.minUsagePrice <= actuallyPrice.value,
  )

  // 如果过滤后的列表为空，则返回null
  if (filteredList.length === 0) {
    return null
  }

  // 在过滤后的列表中找出couponPrice最大的对象
  return filteredList.reduce(
    (max, current) => (current.couponPrice > max.couponPrice ? current : max),
    filteredList[0],
  )
}

/**
 * 找出最优优惠券，
 * @param list
 */
function getOptimalCoupon(list: CouponPageResData[]) {
  // console.log(list)

  // 最优代金券列表
  const bestPriceItem = findMaxPriceCoupon(list)
  // console.log('bestPriceItem', bestPriceItem)

  // 代金券计算出来的实付
  let pricePrice = actuallyPrice.value
  // 折扣券计算出来的实付
  let discountPrice = actuallyPrice.value
  if (bestPriceItem) {
    pricePrice = actuallyPrice.value - bestPriceItem.couponPrice
  }
  if (bestDiscountItem.value) {
    discountPrice = actuallyPrice.value * bestDiscountItem.value.discount
  }
  if (bestPriceItem === null && bestDiscountItem.value === null) {
    couponId.value = null
  }
  if (pricePrice <= discountPrice) {
    // 代金券优惠
    // console.log('bestPriceItem', bestPriceItem)
    couponId.value = bestPriceItem?.couponId
    couponType.value = bestPriceItem?.couponType
    discount.value = bestPriceItem?.discount
    couponPrice.value = bestPriceItem?.couponPrice
  }
  else {
    // 折扣券优惠
    couponId.value = bestDiscountItem.value?.couponId
    couponType.value = bestDiscountItem.value?.couponType
    discount.value = bestDiscountItem.value?.discount
    couponPrice.value = bestDiscountItem.value?.couponPrice
  }
}

/**
 * 选优惠券计算优惠
 */
function getOrderPrice() {
  return new Promise((resolve, reject) => {
    btnDisable.value = true
    const params: SubmitOrderParams = {
      orderCode: orderCode.value,
    }
    if (couponId.value) {
      params.couponId = couponId.value
    }
    submitOrderApi(params)
      .then((res: any) => {
        const d = res.data
        actuallyPrice.value = d.actuallyPrice
        startBarCode.value = d.startBarCode
        endBarCode.value = d.endBarCode
        size.value = d.size
        price.value = d.price
        useCount.value = d.useCount
        existCount.value = d.existCount
        total.value = d.total
        totalPrice.value = d.totalPrice
        discountsPrice.value = d.discountsPrice

        if (useCount.value === 0) {
          btnStr.value = '已生成过，前往下载'
        }
        else {
          btnStr.value = `马上生成，合计：￥${actuallyPrice.value}`
        }
        btnDisable.value = false
        resolve(true)
      })
      .catch((err) => {
        btnDisable.value = false
        useOrderStore.clearCouponInfo()
        reject(err)
      })
  })
}

function sendOrder() {
  const url = '/pages/paymentSuccess/makeFilm'

  btnDisable.value = true
  uni.showLoading({
    title: '支付中',
  })

  payOrderApi({
    orderCode: orderCode.value,
  })
    .then((resData: any) => {
      if (resData.data.isNeedToPay) {
        wxRequestPayment(resData.data)
          .then(() => {
            btnDisable.value = false
            uni.hideLoading()
            uni.redirectTo({
              url,
            })
          })
          .catch(() => {
            btnDisable.value = false
            uni.hideLoading()
          })
      }
      else {
        btnDisable.value = false
        uni.hideLoading()
        uni.redirectTo({
          url,
        })
      }
    })
    .catch((err: string) => {
      btnDisable.value = false
      uni.hideLoading()
      catchErrorAndNavigateBack(err)
    })
}

const handleSubmit = debounce(
  () => {
    if (!btnDisable.value) {
      if (agree.value) {
        if (useCount.value === 0) {
          // 已生成过，前往下载
          uni.switchTab({
            url: '/pages/myFilm/index',
          })
        }
        else {
          sendOrder()
        }
      }
      else {
        useMsgModalStore
          .confirm({
            title: '温馨提示',
            content: '请先勾选已阅读并同意《付款免责声明》',
          })
          .then(() => {
            uni.pageScrollTo({
              selector: '#agreeElement',
            })
          })
      }
    }
  },
  1000,
  { immediate: true },
)

function toDiscountCouponPage() {
  uni.navigateTo({
    url: `/pages/discountCoupon/index?toSelect=true&makeFilmHasOtherServer=${isHasOtherServer.value}`,
  })
}

function thisMonth() {
  // Get the current date
  const currentDate = new Date()

  // Get the first day of the month
  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)

  // Get the last day of the month
  const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)

  // Format the dates to YYYY-MM-DD
  const formatDate = date => `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`

  // Create the result string
  return `${formatDate(firstDayOfMonth)} ~ ${formatDate(lastDayOfMonth)}`
}

function getDiscountPrice(price: number) {
  if (couponType.value === CouponType.discount) {
    const discountedPrice = price * discount.value
    return Number.parseFloat(discountedPrice.toFixed(2)).toString()
  }
  else {
    return price.toString()
  }
}

function getSumPrice(data: PriceTempListResData) {
  // 首个条码
  // 1~9个条码
  // 10~49个条码
  // 50~99个条码
  // 100~199个条码
  // 200个条码以上
  const match = data.tempName.match(/\d+/)
  let sum: string
  if (match) {
    // 数量，200
    const count = Number.parseInt(match[0], 10)
    if (couponType.value === CouponType.discount) {
      sum = getDiscountPrice(data.price * count)
    }
    else {
      sum = `${data.price * count}`
    }
    return `${count}个条码，${sum}元`
  }
  else {
    if (couponType.value === CouponType.discount) {
      sum = getDiscountPrice(data.price)
    }
    else {
      sum = `${data.price}`
    }
    return `首个条码，${sum}元`
  }
}

function handleModalOk() {
  agree.value = true
  isModalShow.value = false
}

onLoad(() => {
  // console.log(option)
  useOrderStore.clearOrderInfo()
  useOrderStore.clearCouponInfo()
  getTempList()
  getInfoReportPriceTemp()
  // 第一次获无优惠券的取订单价格
  getOrderPrice().then(() => {
    getOptimalCoupon(couponList.value)
    getOrderPrice()
  })
})

onShow(() => {
  // onLoad 比 onShow 优先执行
  // 选择优惠券后，后退回到这逻辑
  if (isSelectedCoupon.value) {
    getOrderPrice()
    isSelectedCoupon.value = false
  }
})
</script>

<template>
  <up-modal
    :show="isModalShow"
    confirm-text="同意"
    show-cancel-button
    close-on-click-overlay
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
  >
    <PaidDisclaimer v-if="isModalShow" :article-ids="articleIds" />
  </up-modal>
  <view class="flex items-end justify-between bg-white p-4">
    <view class="pl-2">
      <view class="text-xl text-primary font-bold">
        订单确认-{{ DESCRIPTION_STR[descriptionServiceType].title }}
      </view>
      <view class="o-color-aid text-sm">
        {{ DESCRIPTION_STR[descriptionServiceType].typeStr }}
      </view>
      <view class="text-xs">
        {{ DESCRIPTION_STR[descriptionServiceType].description }}
      </view>
    </view>
  </view>
  <view class="mt-3 px-4">
    <view class="flex flex-col items-center justify-center rd-2 bg-white p-4">
      <view v-if="isShowStep" class="mb-6 pt-2 text-sm">
        <view class="pt-1">
          <view class="mb-2 font-bold">
            收费标准：
          </view>
          <view class="text-xs">
            {{ DESCRIPTION_STR.chargingStandard }}
          </view>
          <view class="mb-2 mt-4 text-red-500 font-bold">
            优惠期间推行阶梯定价：
          </view>
          <view class="f-table text-xs">
            <view class="f-table-title f-table-tr">
              <view class="f-table-td">
                条码数量
              </view>
              <view class="f-table-td">
                每张(元)
              </view>
              <view class="f-table-td">
                合计
              </view>
            </view>
            <view class="f-table-context">
              <view v-for="(item, index) in serveData" :key="item.priceTempId" class="f-table-tr">
                <view class="f-table-td">
                  {{ item.tempName }}
                </view>
                <view class="f-table-td">
                  <text>{{ getDiscountPrice(item.price) }}</text>
                  <up-tag
                    v-if="index === serveData.length - 1"
                    class="ml-1 mt--0.5"
                    style="transform-origin: left center; scale: 0.7"
                    text="最优惠"
                    type="error"
                    size="mini"
                  />
                </view>
                <view class="f-table-td">
                  {{ getSumPrice(item) }}
                </view>
              </view>
            </view>
          </view>
          <view class="mt-1 pl-3 text-xs text-red-500">
            <text class="pr-2">
              *
            </text>
            优惠时间：{{ thisMonth() }}
          </view>
        </view>
      </view>
      <view class="o-color-aid flex items-center gap-3 text-sm" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18" />
        <view>{{ isShowStep ? '隐藏' : '显示' }}收费标准说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16" />
        <up-icon v-else name="arrow-down" size="16" />
      </view>
    </view>
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 mt-2 font-bold">
        要生成条码的编码：
      </view>
      <view class="flex items-center">
        <view class="o-barcode-gray-card rd-1">
          {{ startBarCode }}
        </view>
        <template v-if="endBarCode !== startBarCode">
          <view>~</view>
          <view class="o-barcode-gray-card rd-1">
            {{ endBarCode }}
          </view>
        </template>
      </view>
      <view class="o-color-aid mt-2 text-xs">
        <text class="text-red-500">
          *
        </text>
        校验码显示为 * 号，条码制作时自动生成。如您已有校验码，所生成校验码也会跟其一致，敬请放心。
      </view>
      <view class="mt-3 text-sm">
        放大系数为：
        <text class="font-bold">
          {{ size }}
        </text>
      </view>

      <view class="o-line mb-4 mt-4" />
      <view class="mt-1 text-sm">
        共
        <text class="font-bold">
          {{ total }}
        </text>
        张，其中
        <text class="font-bold">
          {{ existCount }}
        </text>
        张已生成过；
      </view>
      <view class="mt-1 text-sm">
        排除重复后，为
        <text class="font-bold">
          {{ useCount }}
        </text>
        张。
      </view>
      <view class="o-color-aid mt-3 text-xs">
        <text class="text-red-500">
          *
        </text>
        重复的条码可在我的条码中，直接下载。
      </view>
      <view class="o-line mb-4 mt-4" />
      <view class="flex justify-between text-sm">
        <view>
          <text class="font-bold">
            {{ useCount }}
          </text>
          张
          <text>×</text>
          <text class="pl-1 font-bold">
            {{ price }}
          </text>
          元/张
        </view>
        <view>￥{{ barCodePrice }}</view>
      </view>
      <view v-if="isHasOtherServer" class="mt-3 flex items-center justify-between text-sm">
        <view>
          <view>附加服务</view>
          <view class="text-xs color-gray">
            {{ makeFilmAndReportServiceDescription }}
          </view>
        </view>
        <view>￥{{ extraPrice }}</view>
      </view>
      <view class="mt-3 flex justify-between text-sm" @click="toDiscountCouponPage">
        <view>{{ couponTypeStr(couponType) }}</view>
        <view class="flex gap-1">
          <template v-if="couponType === CouponType.cash">
            <view>￥</view>
            <view>{{ couponPrice }}</view>
          </template>
          <discount-box v-if="couponType === CouponType.discount" :discount="discount" :size="28" />
          <view class="o-color-aid pr-1">
            <up-icon name="arrow-right" size="14" />
          </view>
        </view>
      </view>
      <view class="o-line mb-3 mt-3" />
      <view class="flex justify-end">
        <view class="flex items-baseline">
          <view>合计：</view>
          <price-box
            :price="useCount === 0 ? 0 : actuallyPrice"
            :size="48"
            class="text-red-500"
          />
        </view>
      </view>
    </view>
    <view id="agreeElement" class="mt-6 flex items-center justify-center text-xs">
      <up-checkbox
        v-model:checked="agree"
        used-alone
        label-size="12"
        size="14"
        label="我已阅读并同意"
      />
      <view class="text-primary" @click.stop="isModalShow = true">
        《付款免责声明》
      </view>
    </view>
    <view class="p-11" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view
        :class="btnDisable ? 'o-bg-primary-disable' : ' o-bg-primary o-shadow-blue'"
        class="flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold"
        @click="handleSubmit"
      >
        {{ btnStr }}
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-table {
  width: 600rpx;
  margin: 0 auto;
  border: rgba(220, 225, 234, 0.7) 1px solid;
}

.f-table-title {
  background-color: #e8efff;
}

.f-table-context > view:nth-child(even) > view {
  background-color: rgba(220, 225, 234, 0.3);
}

.f-table-tr {
  @apply flex items-center gap-1;

  .f-table-td {
    @apply text-center py-1;

    &:nth-child(1) {
      @apply shrink-0;
      flex-basis: 25vw;
    }

    &:nth-child(2) {
      @apply shrink-0;
      flex-basis: 18vw;
    }

    &:nth-child(3) {
      @apply flex-1;
    }
  }
}

.f-hot::after {
  @apply rd-1 absolute px-2 py-1 top--1 left--2;

  width: 100%;
  height: 100%;
  content: '';
  background-color: rgba(245, 63, 63, 0.05);
  border: 1px dashed var(--wot-color-danger);
}
</style>
