<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "商品标准条码制作",
    "navigationStyle": "custom"
  }
}
</route>

<script lang="ts" setup>
import type { ProjectType } from '@/components/descriptionStr'
import type { MallGoodsPageRes } from '@/service/shoppingMallApi'
import { storeToRefs } from 'pinia'
import RenewalTip from '@/components/agencyService/renewalTip.vue'
import CsRoundButton from '@/components/customer/csRoundButton.vue'
import { PROJECT_1, PROJECT_2 } from '@/components/descriptionStr'
import { DesignImage } from '@/components/image'
import { BarType, ServerType } from '@/enums'
import { useToPath } from '@/hooks/useToPath'
import { ignoreRenewalInformApi } from '@/service/agencyServiceApi'
import { mallGoodsPageApi } from '@/service/shoppingMallApi'
import { wxmalinkSignUrlLinkApi } from '@/service/systemApi'
import { orderStore } from '@/store/orderStore'
import { useServiceStore } from '@/store/serviceStore'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'Home',
})

const RESOURCES_URL = import.meta.env.VITE_RESOURCES_URL

const userStore = useUserStore()
const { userId, dataCode, inBlacklist } = storeToRefs(userStore)
const serviceStore = useServiceStore()
const { descriptionServiceType } = storeToRefs(serviceStore)
const useOrderStore = orderStore()
const { serverType } = storeToRefs(useOrderStore)
const { toPath } = useToPath()

const opacity = ref(0)
const carousesList = DesignImage.indexPage.carouses.map(d => d.img)
const servicesList1 = ref<ProjectType[]>([])
const servicesList2 = ref<ProjectType[]>([])
const fistCompanyCode = ref('')
const isShowRenewalServiceExpire = ref(false)
const companyName = ref('')
const gsDataId = ref(Number.NaN)
const validDate = ref('')
const vendorCode = ref('')

// 计算剩余月份
function getRemainingMonths(dateStr: string) {
  const targetDate = new Date(dateStr)
  const currentDate = new Date()

  // 如果目标日期小于当前日期，则已过期
  if (targetDate < currentDate) {
    return '已过期'
  }

  // 计算天数差
  const diffTime = targetDate.getTime() - currentDate.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  // 如果剩余天数小于31天，显示不足一个月
  if (diffDays < 31) {
    return '不足一个月到期'
  }

  // 计算月份差
  const months = Math.floor(diffDays / 30)
  return `仅剩${months}个月到期`
}

onLoad((option) => {
  console.log('onLoad:', option)
  // 这个是短信转跳
  if ('smsop' in option) {
    // onLoad:{ smsop: "XGxvB"}
    fistCompanyCode.value = option.smsop
  }
  userStore
    .login(fistCompanyCode.value)
    .then(() => {
      // 判断option为空
      // 这里有短信转跳、跟扫码登录pc
      // 扫码登录：option:{scene:"wxlogin%3Dxxxxxxxxxxxxxxxx"}
      if (Object.keys(option).length !== 0) {
        const query = JSON.stringify(option)
        console.log('query:', query)
        wxmalinkSignUrlLinkApi({ userInfoId: userId.value, query })
      }
      if (inBlacklist.value) {
        uni.showLoading({ title: '加载中...', mask: true })
      }
      else {
        getMallData()
      }
      // 用于获取dataCode来传播黑名单分享
      userStore.getUserServerInfo().then((res) => {
        const d = res.data.renewalInformDTO
        if (d.isNeedInform) {
          companyName.value = d.companyName
          gsDataId.value = d.gsDataId
          validDate.value = d.validDate
          vendorCode.value = d.vendorCode
          // 续展弹窗
          isShowRenewalServiceExpire.value = true
        }
      })
    })
    .catch((err) => {
      console.log('登录失败', err)
    })
})

function getPath(): string {
  if (fistCompanyCode.value) {
    return `/pages/index/index?smsop=${fistCompanyCode.value}`
  }
  else if (dataCode.value) {
    return `/pages/index/index?smsop=${dataCode.value}`
  }
  return '/pages/index/index'
}

onShareAppMessage(() => {
  return {
    title: '商用条形码生成器',
    path: getPath(),
  }
})
onShareTimeline(() => {
  // 朋友圈传参，参数因为单页模式限制拿不回来
  return {
    title: '商用条形码生成器',
    path: getPath(),
  }
})

// 绑定分享参数
wx.onCopyUrl(() => {
  if (fistCompanyCode.value) {
    return { query: `smsop=${fistCompanyCode.value}` }
  }
  else if (dataCode.value) {
    return { query: `smsop=${dataCode.value}` }
  }
  return {}
})

onPageScroll((e) => {
  // scrollTop 最小为 0，最大为 40 时 opacity 达到 1
  const top = Math.max(0, Math.min(e.scrollTop, 40))
  opacity.value = top / 40
})

function handleSwiperClick(index: number) {
  if (inBlacklist.value)
    return
  const url = DesignImage.indexPage.carouses[index].url
  if (url === '/pages/makeFilm/index') {
    descriptionServiceType.value = BarType.EAN13
    serverType.value = ServerType.makeFilm
  }
  uni.navigateTo({
    url: DesignImage.indexPage.carouses[index].url,
  })
}

function toTutorialsPage() {
  uni.navigateTo({
    url: '/pages/tutorials/index',
  })
}

function toViolationPage() {
  if (inBlacklist.value)
    return
  uni.navigateTo({
    url: '/pages/tutorials/violation',
  })
}

function getMallData() {
  const toDealList = (
    project: ProjectType[],
    resData: MallGoodsPageRes['data'],
    serviceList: ProjectType[],
  ) => {
    project.forEach((proItem) => {
      if (proItem.getInfoFrom === 'shopMall') {
        const mallItem = resData.find(item => item.goodsId === proItem.goodsId)
        if (mallItem !== undefined) {
          serviceList.push({
            descriptionServiceType: proItem.descriptionServiceType,
            tagBgColor: proItem.tagBgColor,
            tagStrColor: proItem.tagStrColor,
            image: proItem.image,
            title: mallItem.goodsName,
            typeStr: proItem.typeStr,
            description: proItem.description,
            url: `${proItem.url}?goodsId=${mallItem.goodsId}`,
            getInfoFrom: proItem.getInfoFrom,
            goodsId: mallItem.goodsId,
          })
        }
      }
      else if (proItem.getInfoFrom === 'local') {
        serviceList.push(proItem)
      }
    })
  }
  mallGoodsPageApi({}).then((res) => {
    const mallData = res.data
    toDealList(PROJECT_1, mallData, servicesList1.value)
    toDealList(PROJECT_2, mallData, servicesList2.value)
  })
}

function toRenewalService() {
  isShowRenewalServiceExpire.value = false
  const item = PROJECT_2.find(item => item.descriptionServiceType === 'agencyService')
  toPath(item, 'navigateTo')
}

function dontRemind() {
  isShowRenewalServiceExpire.value = false
  ignoreRenewalInformApi({ gsDataId: gsDataId.value })
}
</script>

<template>
  <view :style="{ opacity }" style="position: relative; z-index: 9999; height: 0">
    <up-navbar placeholder safeareainsettop fixed>
      <template #left>
        <image
          :src="`${RESOURCES_URL}/p_index_top_logo.png`"
          alt="条码帮"
          class="ml-2"
          mode="aspectFit"
          style="width: 42vw; height: 10vw"
        />
      </template>
    </up-navbar>
  </view>
  <view class="f-bg overflow-hidden p-3">
    <view class="f-p" />
    <view class="f-shadow">
      <swiper class="f-swiper overflow-hidden rd-2" circular :autoplay="true" :interval="3000">
        <swiper-item
          v-for="(item, index) in carousesList"
          :key="index"
          @click="handleSwiperClick(index)"
        >
          <image :src="item" class="f-swiper w-full" />
        </swiper-item>
      </swiper>
    </view>
    <view class="mt-2 flex justify-between">
      <view class="f-chip-green rd" @click="toTutorialsPage" />
      <view class="f-chip-yellow rd" @click="toViolationPage" />
    </view>
    <view class="mb-1 mt-3 flex overflow-hidden rounded">
      <view class="o-bg-primary o-shadow-blue f-title-ii" />
      <view class="f-title-i" />
      <view class="f-title-bg py-1 pl-2">
        热门业务
      </view>
    </view>
    <view class="grid grid-cols-3 gap-1">
      <view
        v-for="item in servicesList1"
        :key="item.descriptionServiceType"
        class="f-list flex flex-col items-center rd-2 bg-white p-2"
        @click="toPath(item, 'navigateTo')"
      >
        <image :src="item.image" style="width: 88px; height: 61.2px" />
        <view class="mt-2 text-sm font-bold">
          {{ item.title }}
        </view>
        <view class="text-center text-xs color-gray">
          {{ item.description }}
        </view>
      </view>
    </view>
    <view class="mb-1 mt-1 flex overflow-hidden rounded">
      <view class="o-bg-primary o-shadow-blue f-title-ii" />
      <view class="f-title-i" />
      <view class="f-title-bg py-1 pl-2">
        常规业务
      </view>
    </view>
    <view class="grid grid-cols-3 gap-1">
      <view
        v-for="item in servicesList2"
        :key="item.descriptionServiceType"
        class="flex flex-col items-center rd-2 bg-white p-2"
        @click="toPath(item, 'navigateTo')"
      >
        <image :src="item.image" style="width: 88px; height: 61.2px" />
        <view class="mt-2 text-sm font-bold">
          {{ item.title }}
        </view>
        <view class="text-center text-xs color-gray">
          {{ item.description }}
        </view>
      </view>
    </view>
    <!-- <view class="py-6" /> -->
    <view class="o-pb" />
  </view>
  <up-modal
    :show="isShowRenewalServiceExpire"
    title="条码即将到期"
    confirm-text="线上代办"
    :show-cancel-button="true"
    cancel-text="下次提醒"
    @confirm="toRenewalService"
    @cancel="isShowRenewalServiceExpire = false"
  >
    <view class="slot-content">
      <view class="o-p">
        您的厂商识别码：
        <text class="font-bold">
          {{ vendorCode }}
        </text>
        （
        <text class="font-bold">
          {{ companyName }}
        </text>
        ），
        <text class="font-bold">
          {{ getRemainingMonths(validDate) }}
        </text>
        ，请及时办理续展业务。
      </view>
      <view class="o-line my-3" />
      <view class="mb-1 text-sm font-bold">
        条码过期怎么办：
      </view>
      <RenewalTip class="text-xs" />
      <view class="mt-4 text-xs">
        <text class="text-red-500">
          *
        </text>
        如已办理，请点击
        <text class="text-primary underline" @click="dontRemind">
          不再提醒
        </text>
      </view>
    </view>
  </up-modal>
  <cs-round-button v-if="!inBlacklist" />
</template>

<style lang="scss" scoped>
.f-shadow {
  box-shadow: 0 21px 11.4px -15px rgba(74, 124, 239, 0.48);
}

.f-bg {
  background-image: url('https://wx.gs1helper.com/images/p_index_head_X.jpg');
  background-repeat: no-repeat;
  background-size: 100% auto;
}

.f-p {
  padding-top: 28vw;
}

.f-title-ii {
  width: 11.81rpx;
  height: 60rpx;
}

.f-title-i {
  width: 6.94rpx;
  height: 60rpx;
  margin-left: 7rpx;
  background-color: #16b3ff;
}

.f-title-bg {
  background: linear-gradient(90deg, #e8efff 62.5%, rgba(232, 239, 255, 0) 100%);
}

.f-list {
  &:nth-child(1) {
    background: linear-gradient(224deg, #ffecd8 -20.14%, #fff 50.89%);
  }

  &:nth-child(2) {
    background: linear-gradient(224deg, #c5d6ff -20.14%, #fff 50.89%);
  }
}

$ch: 130rpx;

.f-chip-green {
  width: 332rpx;
  height: $ch;
  background-image: url('https://wx.gs1helper.com/images/p_index_chip_green_X.png');
  background-size: cover;
  box-shadow: 0 21px 11.4px -15px rgba(22, 93, 255, 0.33);
}

.f-chip-yellow {
  width: 332rpx;
  height: $ch;
  background-image: url('https://wx.gs1helper.com/images/p_index_chip_yellow_X.png');
  background-size: cover;
  box-shadow: 0 21px 11.4px -15px rgba(177, 150, 63, 0.53);
}

.f-swiper {
  height: 271rpx;
}
</style>
