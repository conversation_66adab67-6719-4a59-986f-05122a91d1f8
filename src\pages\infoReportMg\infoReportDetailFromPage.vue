<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "产品详情"
  }
}
</route>

<script lang="ts" setup>
import type { AttributeListItem, ReportGoodsBaseParams } from '@/service/infoReportApi'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import ShowFormTip from '@/components/showFormTip/index.vue'
import { CurrencyType, GoodsType, InfoReportBarType } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { useFormTip } from '@/hooks/useFormTip'
import { gpcAttributeListApi, gs1NetContentListApi, reportGoodsAddApi } from '@/service/infoReportApi'
import { InfoReportGoodsImageUploadPath } from '@/service/stsTokenApi'
import { updateUserPhoneApi } from '@/service/systemApi'
import { infoReportDetailStore } from '@/store/infoReportDetailStore'
import { infoReportEditStore } from '@/store/infoReportEditStore'
import { msgModalStore } from '@/store/msgModalStore'
import { ossTokenStore } from '@/store/ossTokenStore'
import { useUserStore } from '@/store/user'
import { showErrorAndScrollTo, validateForm } from '@/utils/formValidation'
import { validateOSSConfig } from '@/utils/ossUtils'

const RESOURCES_URL = import.meta.env.VITE_RESOURCES_URL

const useMsgModalStore = msgModalStore()
const userStore = useUserStore()
const { userId, barCodeCardNum, barCodeCardPassword, phone, isAccountError } = storeToRefs(userStore)
const useInfoReportDetailStore = infoReportDetailStore()
const { infoReportDetailData, gpcTypeName } = storeToRefs(useInfoReportDetailStore)
const useInfoReportEditStore = infoReportEditStore()
const useOssTokenStore = ossTokenStore()
const { ossConfig } = storeToRefs(useOssTokenStore)
const { showTip } = useFormTip()
const isCreate = ref<boolean>(false)
const isLoading = ref<boolean>(false)
const isAutoCombination = ref<boolean>(true)
const isShowDatePicker = ref<boolean>(false)
const isShowKeyModal = ref<boolean>(false)
const isShowExecuteStandardPicker = ref<boolean>(false)
const isShowAttributePicker = ref<boolean>(false)
const isShowNetContentUnitPicker = ref<boolean>(false)
const rawMarketDate = ref(0)
const currencyTypeList = Object.values(CurrencyType)
const executeStandardIndex = ref(0)
const attributePickerIndex = ref(0)
const attributePickerValue = ref('')
const executeStandardPickerDefaultIndex = ref([[0]])
const netContentUnitPickerDefaultIndex = ref([[0]])
const attributePickerDefaultIndex = ref([[0]])
const attributePickerColumns = ref<string[][]>([])
const netContentUnitPickerColumns = ref<string[][]>([])
const executeStandardPickerColumns = [
  ['GB', 'GB/Z', 'NY', 'GB/T', 'GBZ/T', 'NY/T'],
]
const goodsImages = ref<{
  path: string
  status: string
  message: string
  url: string
  realUrl: string
}[]>([])
const MaxImageCount = 5
const goodsId = ref<number>()
const activeGpcItem = ref(
  {
    code: '',
    gpcTypeName: '',
  },
)
const formData = reactive<ReportGoodsBaseParams>({
  attribueList: [],
  barCode: '',
  barType: InfoReportBarType.EAN,
  brandName: '',
  commonName: '',
  companyPrice: null,
  currency: CurrencyType.RMB,
  goodsDescription: '',
  goodsName: '',
  goodsType: GoodsType.inProduction,
  gpcType: '',
  imageList: [],
  isPrivary: false,
  marketDate: '',
  netContent: '',
  netContentUnit: '',
  productFeatures: '',
  spec: '',
  standardList: [
    {
      executeStandard: '',
      executeYear: '',
      standardNumber: '',
    },
  ],
})

const attributeListForm = ref<AttributeListItem[]>([])

function saveDate() {
  // 用day.js时间戳转换为年月
  const year = dayjs(rawMarketDate.value).format('YYYY')
  const month = dayjs(rawMarketDate.value).format('MM')

  formData.marketDate = `${year}-${month}`
  isShowDatePicker.value = false
}

function toSelectGpc() {
  uni.navigateTo({
    url: '/pages/infoReportMg/selectGpcPage',
  })
}

function toSelectBand() {
  uni.navigateTo({
    url: '/pages/brand/brandListPage',
  })
}

function showExecuteStandardPicker(index: number) {
  executeStandardIndex.value = index
  executeStandardPickerDefaultIndex.value = [[executeStandardPickerColumns[0].findIndex(
    item => item === formData.standardList[index].executeStandard,
  )]]
  if (executeStandardPickerDefaultIndex.value[0][0] === -1) {
    executeStandardPickerDefaultIndex.value[0][0] = 0
  }
  isShowExecuteStandardPicker.value = true
}

function executeStandardConfirm(e) {
  formData.standardList[executeStandardIndex.value].executeStandard = e.value[0]
  isShowExecuteStandardPicker.value = false
}

function attributePickerConfirm(e) {
  if (formData.attribueList[attributePickerIndex.value]) {
    formData.attribueList[attributePickerIndex.value].attributeValue = e.value[0]
  }
  isShowAttributePicker.value = false
}

function netContentUnitPickerConfirm(e) {
  formData.netContentUnit = e.value[0]
  netContentUnitPickerDefaultIndex.value = [[netContentUnitPickerColumns.value[0].findIndex(
    item => item === formData.netContentUnit,
  )]]
  isShowNetContentUnitPicker.value = false
}

function handleMinusStandard(index: number) {
  if (formData.standardList.length > 1) {
    // 删除当前项
    formData.standardList.splice(index, 1)
  }
}

function handleAddStandard() {
  formData.standardList.push({
    executeStandard: '',
    executeYear: '',
    standardNumber: '',
  })
}

// 预览图片
function previewImage(current: string) {
  // 收集所有成功上传的图片URL
  const urls = goodsImages.value.filter(img => img.status === 'success').map(img => img.url)
  if (urls.length > 0) {
    uni.previewImage({
      urls,
      current,
    })
  }
}

function putImagesInData() {
  formData.imageList = goodsImages.value.filter(img => img.status === 'success').map(img => ({
    imageDecs: '',
    imageUrl: img.realUrl,
    isMain: false,
  }))
  formData.imageList[0].isMain = true
}

// 处理图片上传
async function handleGoodsFileUpload(res: UniApp.ChooseImageSuccessCallbackResult) {
  const files = res.tempFilePaths as string[]
  let fileListLen = goodsImages.value.length

  console.log('准备上传图片:', files.length, '张')

  // 确保OSS token有效
  const tokenReady = await useOssTokenStore.getOssToken()
  if (!tokenReady) {
    uni.showToast({
      title: '获取上传凭证失败，请重试',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 添加上传状态
  for (let i = 0; i < files.length; i++) {
    const path = files[i]
    goodsImages.value.push({
      path,
      status: 'uploading',
      message: '上传中',
      url: path,
      realUrl: '',
    })
  }
  // 逐个上传并更新状态
  for (let i = 0; i < files.length; i++) {
    try {
      console.log(`开始上传第 ${i + 1}/${files.length} 张图片`)
      const item = goodsImages.value[fileListLen]
      const fileUrl = files[i]

      console.log('文件URL:', fileUrl)
      const result = await uploadGoodsFilePromise(fileUrl)

      console.log('上传成功，结果:', result)

      // 更新文件状态
      goodsImages.value.splice(fileListLen, 1, {
        ...item,
        status: 'success',
        message: '',
        // 使用OSS返回的完整URL作为预览图片地址
        url: result.url || '',
        realUrl: result.url,
      })
      fileListLen++
    }
    catch (error) {
      console.error(`第 ${i + 1}/${files.length} 张图片上传失败:`, error)

      // 上传失败处理
      const item = goodsImages.value[fileListLen]
      goodsImages.value.splice(fileListLen, 1, {
        ...item,
        status: 'failed',
        message: `上传失败: ${(error as Error).message || '未知错误'}`,
      })
      fileListLen++

      uni.showToast({
        title: `图片上传失败: ${(error as Error).message || '未知错误'}`,
        icon: 'none',
        duration: 2000,
      })
    }
  }

  // 更新图片字符串
  putImagesInData()
}

// 获取文件后缀
function getSuffix(name: string): string {
  return name.substring(name.lastIndexOf('.'))
}

// 生成随机字符串
function randomString(len: number): string {
  const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  const maxPos = chars.length
  let str = ''
  for (let i = 0; i < len; i++) {
    str += chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return str
}

// OSS上传函数
function ossUpload(filePath: string, dir: string) {
  return new Promise<{
    success: boolean
    url?: string
    image?: string
    errorMessage?: string
  }>((resolve, reject) => {
    // 验证OSS配置是否完整
    if (!validateOSSConfig(ossConfig.value)) {
      const errorMsg = 'OSS配置不完整，请重新获取上传凭证'
      console.error(errorMsg, ossConfig.value)
      reject(new Error(errorMsg))
      return
    }

    const name = filePath.substring(filePath.lastIndexOf('/') + 1)
    const extension = getSuffix(name)
    // 生成包含时间戳的文件名：目录/时间戳_随机字符串.扩展名
    const timestamp = new Date().getTime()
    const key = `${dir}/${timestamp}_${randomString(6)}${extension}`

    console.log('开始OSS上传:', {
      filePath,
      key,
      url: ossConfig.value.url,
      hasPolicy: !!ossConfig.value.policy,
      hasSignature: !!ossConfig.value.signature,
    })

    uni.uploadFile({
      url: ossConfig.value.url,
      filePath,
      name: 'file',
      formData: {
        name,
        key,
        'OSSAccessKeyId': ossConfig.value.accessKeyId,
        'success_action_status': '200',
        'policy': ossConfig.value.policy,
        'signature': ossConfig.value.signature,
        'x-oss-security-token': ossConfig.value.securityToken,
      },
      success: (uploadRes) => {
        console.log('OSS上传成功响应:', uploadRes)

        // 确保URL是完整的，包含协议前缀
        let fullUrl = ossConfig.value.url
        if (!fullUrl.endsWith('/')) {
          fullUrl += '/'
        }
        fullUrl += key

        if (!fullUrl.startsWith('http')) {
          fullUrl = `https://${fullUrl}`
        }

        console.log('上传成功，完整URL:', fullUrl)
        resolve({
          success: true,
          url: fullUrl,
          image: key.split('/').pop(),
        })
      },
      fail: (error) => {
        console.error('OSS上传失败:', error)
        const errorMsg = error.errMsg || '上传失败'
        reject(new Error(errorMsg))
      },
    })
  })
}

// 上传评价图片Promise
async function uploadGoodsFilePromise(url: string) {
  try {
    if (!url) {
      throw new Error('文件路径为空')
    }

    console.log('开始上传图片路径:', url)

    // 使用ossUpload上传到OSS
    const result = await ossUpload(url, InfoReportGoodsImageUploadPath)
    console.log('上传结果:', result)

    if (result.success) {
      return result
    }
    else {
      console.error('上传失败，错误信息:', result.errorMessage)
      uni.showToast({
        title: `图片上传失败: ${result.errorMessage || '未知错误'}`,
        icon: 'none',
        duration: 2000,
      })
      throw new Error(result.errorMessage || '上传失败')
    }
  }
  catch (error) {
    console.error('上传失败:', error)
    uni.showToast({
      title: '图片上传失败，请重试',
      icon: 'none',
      duration: 2000,
    })
    throw error
  }
}

// 删除评价图片
function deleteGoodsImage(index: number) {
  goodsImages.value.splice(index, 1)
  putImagesInData()
}

// 选择图片
function chooseImages() {
  if (goodsImages.value.length >= MaxImageCount) {
    uni.showToast({
      title: `最多只能上传${MaxImageCount}张图片`,
      icon: 'none',
      duration: 2000,
    })
    return
  }

  const count = MaxImageCount - goodsImages.value.length

  uni.chooseImage({
    count,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      handleGoodsFileUpload(res)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
    },
  })
}

function showImgDemo() {
  uni.previewImage({
    urls: [`${RESOURCES_URL}/infoReportGoodsImageDemo.jpg`],
    current: 0,
  })
}

function showAttributePicker(itemData: AttributeListItem, index: number) {
  console.log('🚀 ~ showAttributePicker ~ attributePickerColumns:', attributePickerColumns.value)
  attributePickerIndex.value = index

  const arr = []
  itemData.attributeValueList.forEach((item) => {
    arr.push(item.attributeValue)
  })

  attributePickerColumns.value = [arr]
  attributePickerDefaultIndex.value = [[attributePickerColumns.value[0].findIndex(
    item => item === formData.attribueList[index].attributeValue,
  )]]
  isShowAttributePicker.value = true
}

function getNetContentList() {
  gs1NetContentListApi().then((res) => {
    const arr = []
    res.data.forEach((item) => {
      arr.push(item.codeUomnCname)
    })
    netContentUnitPickerColumns.value = [arr]
  })
}

function getGpcAttributeList() {
  isLoading.value = true
  gpcAttributeListApi({
    code: activeGpcItem.value.code,
    pageIndex: 1,
    pageSize: 99999,
  }).then((res) => {
    if (res.data.attributeList.length > 0) {
      attributeListForm.value = res.data.attributeList
      const arr: ReportGoodsBaseParams['attribueList'] = []
      res.data.attributeList.forEach((item: AttributeListItem) => {
        arr.push({
          attributeId: item.attributeId,
          attributeName: item.attributeCntitle,
          attributeValue: '',
        })
      })
      formData.attribueList = arr
    }
    else {
      // 清空数组如果没有属性
      attributeListForm.value = []
      formData.attribueList = []
    }
  }).catch((error) => {
    console.error('获取GPC属性列表失败:', error)
    attributeListForm.value = []
    formData.attribueList = []
  }).finally(() => {
    isLoading.value = false
  })
}

function handleSubmit() {
  if (isLoading.value)
    return

  // 设置GPC类型
  if (activeGpcItem.value.code && activeGpcItem.value.code !== '') {
    formData.gpcType = activeGpcItem.value.code
  }

  // 定义验证规则
  const validationRules = [
    {
      condition: !activeGpcItem.value.code || activeGpcItem.value.code === '',
      message: '请选择GPC',
      elementId: 'gpcTypeName',
    },
    {
      condition: formData.commonName === '',
      message: '请输入通用名',
      elementId: 'commonName',
    },
    {
      condition: formData.goodsName === '',
      message: '请输入产品名称',
      elementId: 'goodsName',
    },
    {
      condition: formData.netContent === '',
      message: '请输入净含量',
      elementId: 'netContent',
    },
    {
      condition: formData.netContentUnit === '',
      message: '请选择净含量单位',
      elementId: 'netContentUnit',
    },
    {
      condition: formData.spec === '',
      message: '请输入规格',
      elementId: 'spec',
    },
    {
      condition: formData.goodsDescription === '',
      message: '请输入产品描述',
      elementId: 'goodsDescription',
    },
  ]

  // 执行基础验证
  if (!validateForm(validationRules)) {
    return
  }

  // 验证必填属性
  for (let i = 0; i < attributeListForm.value.length; i++) {
    if (attributeListForm.value[i].isRequired && formData.attribueList[i].attributeValue === '') {
      showErrorAndScrollTo(`请输入${attributeListForm.value[i].attributeCntitle}`, `attribute${i}`)
      return
    }
  }

  // TODO 判断是否要弹出账号密码输入框
  if (barCodeCardNum.value === '' || barCodeCardPassword.value === '' || isAccountError.value) {
    isShowKeyModal.value = true
  }
  else {
    const params = {
      ...formData,
      userId: userId.value,
    }
    if (params.imageList.length === 0 || params.imageList[0].imageUrl === '') {
      delete params.imageList
    }
    reportGoodsAddApi(params).then(() => {
      isShowKeyModal.value = false
      useMsgModalStore.alert({
        title: '已提交通报',
        content: '请留意后续反馈是否成功通报。',
      }).finally(() => {
        uni.navigateBack()
      })
    })
  }
}

function justSaveProduct() {
  // TODO 测试ImageList是否必填
  const params = {
    ...formData,
    userId: userId.value,
  }
  if (params.imageList.length === 0 || params.imageList[0].imageUrl === '') {
    delete params.imageList
  }
  reportGoodsAddApi(params).then(() => {
    isShowKeyModal.value = false
    useMsgModalStore.alert({
      title: '已保存产品',
    }).finally(() => {
      uni.navigateBack()
    })
  })
}

function savePasswordAndReport() {
  if (barCodeCardNum.value === '' || barCodeCardPassword.value === '') {
    uni.showToast({
      title: '请输入条码卡号和密码',
      icon: 'none',
      duration: 2000,
    })
  }
  else {
    // TODO 测试ImageList是否必填
    const params = {
      ...formData,
      userId: userId.value,
    }
    if (params.imageList.length === 0 || params.imageList[0].imageUrl === '') {
      delete params.imageList
    }
    Promise.all([updateUserPhoneApi({
      barCodeCardNum: barCodeCardNum.value,
      barCodeCardPassword: barCodeCardPassword.value,
      phone: phone.value,
      userId: userId.value,
    }), reportGoodsAddApi(params)]).then(() => {
      isShowKeyModal.value = false
      useMsgModalStore.alert({
        title: '已提交通报',
        content: '请留意后续反馈是否成功通报。',
      }).finally(() => {
        uni.navigateBack()
      })
    })
  }
}

watch([isAutoCombination, () => formData.brandName, () => formData.goodsName, () => formData.productFeatures, () => formData.netContent, () => formData.netContentUnit], () => {
  if (isAutoCombination.value) {
    formData.goodsDescription = `${formData.brandName}${formData.goodsName}${formData.productFeatures}${formData.netContent}${formData.netContentUnit}`
  }
})

onShow(() => {
  activeGpcItem.value = useInfoReportEditStore.getActiveGpcItem()
  formData.attribueList = []
  if (activeGpcItem.value?.code && activeGpcItem.value?.code !== '') {
    getGpcAttributeList()
  }
  formData.brandName = useInfoReportEditStore.getBrandName()
})

onLoad((options) => {
  // 获取STS临时凭证
  useOssTokenStore.getOssToken()
  getNetContentList()
  if (options.action === 'create') {
    isCreate.value = true
    uni.setNavigationBarTitle({
      title: '新增产品',
    })
    if (options.barCode) {
      formData.barCode = options.barCode
      if (formData.barCode.charAt(0) === '0') {
        formData.barType = InfoReportBarType.EAN
      }
    }
  }
  else {
    isCreate.value = false
    if (options.goodsId) {
      goodsId.value = Number(options.goodsId)
    }
    uni.setNavigationBarTitle({
      title: '编辑产品',
    })

    // 从store获取数据并赋值
    if (infoReportDetailData.value) {
      Object.assign(formData, infoReportDetailData.value)
      activeGpcItem.value.gpcTypeName = gpcTypeName.value
      activeGpcItem.value.code = infoReportDetailData.value.gpcType
      // TODO 检查图片是否赋值正常
      goodsImages.value = infoReportDetailData.value.imageList.map((item) => {
        return {
          path: '',
          status: 'success',
          message: '',
          url: '',
          realUrl: item.imageUrl,
        }
      })
    }
  }
})

onBeforeUnmount(() => {
  useInfoReportEditStore.clearReportEditData()
})
</script>

<template>
  <view class="p-2 space-y-2">
    <view class="rounded-md bg-white p-4">
      <view class="mt-2">
        <view class="mb-1 flex items-center space-x-2">
          <view class="text-gray-500">
            条码编码
          </view>
          <view class="o-tag border border-primary rounded border-solid bg-primary/10 text-primary">
            {{ formData?.barType }}
          </view>
        </view>
        <view class="o-barcode-gray-card rd-1 text-center font-bold" style="font-size:7.5vw;">
          {{ formData?.barCode }}
        </view>
      </view>
      <view class="f-form-item mt-1">
        <view class="f-label" @click="showTip('是否保密', '保密状态下，不支持对外数据查询和数据共享。')">
          <view class="pr-1">
            是否保密
          </view>
          <up-icon name="question-circle" :color="Color.gray" />
        </view>
        <view class="flex items-center">
          <text class="pr-2">
            {{ formData.isPrivary ? '保密中' : '公开中' }}
          </text>
          <up-switch v-model="formData.isPrivary" :active-color="Color.primary" />
        </view>
      </view>
    </view>
    <view class="rounded-md bg-white p-4 space-y-4">
      <view class="text-lg text-gray-500 font-bold">
        产品身份信息
      </view>
      <view id="gpcTypeName" class="f-form-item">
        <view class="f-label">
          <text class="o-form-require pr-1">
            产品分类
          </text>
        </view>
        <view class="o-form-underline flex flex-1 items-center" @click="toSelectGpc">
          <view class="flex-1 pr-2">
            <text v-if="activeGpcItem?.gpcTypeName">
              {{ activeGpcItem?.gpcTypeName }}
            </text>
            <text v-else class="text-gray">
              请选择GPC分类
            </text>
          </view>
          <up-icon name="arrow-right" :color="Color.gray" class="shrink-0" />
        </view>
      </view>
      <view id="commonName" class="f-form-item">
        <view class="f-label" @click="showTip('通用名', '产品或服务在行业内的通用名称，例如洗衣液、牙膏、薯片。')">
          <text class="o-form-require pr-1">
            通用名
          </text>
          <up-icon name="question-circle" :color="Color.gray" />
        </view>
        <view class="o-form-underline flex-1">
          <input v-model="formData.commonName" placeholder="例：洗发水、手机、饼干" placeholder-style="color: #9ca3af;">
        </view>
      </view>
      <view id="brandName" class="f-form-item">
        <view class="f-label">
          <text class="o-form-require pr-1">
            品牌名称
          </text>
        </view>
        <view class="o-form-underline flex flex-1 items-center" @click="toSelectBand">
          <text class="flex-1 pr-2">
            <text v-if="formData.brandName">
              {{ formData.brandName }}
            </text>
            <text v-else class="text-gray">
              请选择品牌
            </text>
          </text>
          <up-icon name="arrow-right" :color="Color.gray" class="shrink-0" />
        </view>
      </view>
      <view id="goodsName" class="f-form-item">
        <view class="f-label" @click="showTip('产品名称', '填写产品外包装上标示的产品名称。')">
          <text class="o-form-require pr-1">
            产品名称
          </text>
          <up-icon name="question-circle" :color="Color.gray" />
        </view>
        <view class="o-form-underline flex-1">
          <input v-model="formData.goodsName" placeholder="产品外包装上标示的产品名称" placeholder-style="color: #9ca3af;">
        </view>
      </view>
      <view class="f-form-item">
        <view class="f-label" @click="showTip('产品特征', '区分相同品牌和规格产品的最显著特征、功能、特殊风味、香味等。例：去屑、草莓味。')">
          <text class="pr-1">
            产品特征
          </text>
          <up-icon name="question-circle" :color="Color.gray" />
        </view>
        <view class="o-form-underline flex-1">
          <input v-model="formData.productFeatures" placeholder="例：颜色、口味、气味、功能等" placeholder-style="color: #9ca3af;">
        </view>
      </view>
      <view id="netContent" class="f-form-item">
        <view class="f-label" @click="showTip('净含量', '包装上标示的商品净含量数值，与净含量单位一起配合使用，如：350ml、280g。允许范围大于等于0，小于10000。')">
          <text class="o-form-require pr-1">
            净含量
          </text>
          <up-icon name="question-circle" :color="Color.gray" />
        </view>
        <view class="o-form-underline flex-1">
          <input v-model="formData.netContent" placeholder="0~10000，只填数字" placeholder-style="color: #9ca3af;">
        </view>
      </view>
      <view id="netContentUnit" class="f-form-item">
        <view class="f-label">
          <text class="o-form-require pr-1">
            净含量单位
          </text>
        </view>
        <view class="o-form-underline flex flex-1 items-center" @click="isShowNetContentUnitPicker = true">
          <text class="flex-1 pr-2">
            <text v-if="formData.netContentUnit">
              {{ formData.netContentUnit }}
            </text>
            <text v-else class="text-gray">
              请选择净含量单位
            </text>
          </text>
          <up-icon name="arrow-right" :color="Color.gray" class="shrink-0" />
        </view>
      </view>
      <view id="spec" class="f-form-item">
        <view class="f-label" @click="showTip('规格', '指产品的形状、体积、大小和材质等物理特性的描述，可与净含量相同。20字以内。')">
          <text class="o-form-require pr-1">
            规格
          </text>
          <up-icon name="question-circle" :color="Color.gray" />
        </view>
        <view class="o-form-underline flex-1">
          <input v-model="formData.spec" placeholder="20字以内" placeholder-style="color: #9ca3af;">
        </view>
      </view>
      <view id="goodsDescription" class="f-form-item">
        <view class="f-label" @click="showTip('产品描述', '默认由品牌名称+产品名称+产品特征+净含量及单位组成。可根据实际修改。')">
          <text class="o-form-require pr-1">
            产品描述
          </text>
          <up-icon name="question-circle" :color="Color.gray" />
        </view>
        <view class="o-form-underline flex-1">
          <input
            v-model="formData.goodsDescription" placeholder="可根据实际修改" placeholder-style="color: #9ca3af;"
            @input="isAutoCombination = false"
          >
        </view>
      </view>
      <view class="w-full flex items-center justify-end" style="margin-top:0.2rem">
        <text class="pr-2" :class="!isAutoCombination && 'text-gray-500'">
          默认组合：{{ isAutoCombination ? '是' : '否' }}
        </text>
        <up-switch v-model="isAutoCombination" :active-color="Color.primary" />
      </view>
      <view id="goodsType" class="f-form-item">
        <view class="f-label">
          <text class="o-form-require pr-1">
            产品状态
          </text>
        </view>
        <up-radio-group v-model="formData.goodsType" placement="row">
          <up-radio :active-color="Color.primary" :label="GoodsType.inProduction" :name="GoodsType.inProduction" />
          <up-radio
            :active-color="Color.primary" :label="GoodsType.notInProduction"
            :name="GoodsType.notInProduction"
          />
        </up-radio-group>
      </view>
    </view>
    <view class="rounded-md bg-white p-4 space-y-4">
      <view class="flex justify-between">
        <view
          class="f-label"
          @click="showTip('产品图片', '【要求】：商品必须站图片的三分之二以上，不能包含水印、文本；图片必须为jpg或png格式；图片大小不得超过5MB。【建议】：至少上传主图及标签图；图片不低于800×800像素；背景为纯白色。')"
        >
          <text class="pr-1">
            产品图片
          </text>
          <up-icon name="question-circle" :color="Color.gray" />
        </view>
        <view class="text-primary" @click="showImgDemo">
          示例图片
        </view>
      </view>

      <!-- 自定义图片上传组件 -->
      <view class="flex flex-wrap">
        <!-- 已上传的图片 -->
        <view
          v-for="(item, index) in goodsImages" :key="index"
          class="relative m-2 h-20 w-20 overflow-hidden rounded-2"
        >
          <!-- 图片预览 -->
          <image
            :src="item.url" mode="aspectFill" class="h-full w-full object-cover"
            @tap="item.status === 'success' && previewImage(item.url)" @error="
              (e) => {
                console.error('图片加载失败:', item.url, e)
              }
            "
          />
          <!-- 图片状态指示器 -->
          <view
            v-if="item.status === 'uploading'"
            class="absolute left-0 top-0 h-full w-full center flex-col bg-black bg-opacity-50"
          >
            <view class="h-5 w-5 animate-spin border-2 border-white border-t-transparent rounded-full" />
            <text class="mt-2 text-xs text-white">
              上传中
            </text>
          </view>
          <view
            v-if="item.status === 'failed'"
            class="absolute left-0 top-0 h-full w-full center flex-col bg-red-500 bg-opacity-30"
          >
            <text class="mt-2 text-xs text-white">
              上传失败
            </text>
          </view>
          <!-- 删除按钮 -->
          <view
            class="absolute right-0 top-0 h-5 w-5 center rounded-full bg-black bg-opacity-70"
            @tap.stop="deleteGoodsImage(index)"
          >
            <text class="text-base text-white leading-none">
              ×
            </text>
          </view>
        </view>

        <!-- 添加图片按钮 -->
        <view
          v-if="goodsImages.length < MaxImageCount"
          class="m-2 h-20 w-20 center flex-col border border-gray-300 rounded-2 border-dashed bg-gray-100"
          @tap="chooseImages"
        >
          <text class="text-2xl text-gray-400 leading-none">
            +
          </text>
          <text class="mt-2 text-xs text-gray-400">
            添加图片
          </text>
        </view>
      </view>
    </view>
    <view class="rounded-md bg-white p-4 space-y-4">
      <view class="text-lg text-gray-500 font-bold">
        流通信息
      </view>
      <view class="f-form-item">
        <view class="f-label">
          <text class="pr-1">
            (预计)上市日期
          </text>
        </view>
        <view class="o-form-underline flex flex-1 items-center" @click="isShowDatePicker = true">
          <view class="flex-1 pr-2">
            <text v-if="formData?.marketDate">
              {{ formData?.marketDate }}
            </text>
            <text v-else class="text-gray">
              请选择日期
            </text>
          </view>
          <up-icon name="arrow-right" :color="Color.gray" class="shrink-0" />
        </view>
      </view>
      <view class="f-form-item">
        <view class="f-label" @click="showTip('净含量', '包装上标示的商品净含量数值，与净含量单位一起配合使用，如：350ml、280g。允许范围大于等于0，小于10000。')">
          <text class="pr-1">
            企业定价
          </text>
          <up-icon name="question-circle" :color="Color.gray" />
        </view>
        <view class="o-form-underline flex-1">
          <input
            v-model="formData.companyPrice" placeholder="请输入企业定价" type="number"
            placeholder-style="color: #9ca3af;"
          >
        </view>
      </view>
      <view class="f-form-item">
        <view class="f-label">
          <text class="pr-1">
            币种
          </text>
        </view>
        <up-radio-group v-model="formData.currency" placement="row">
          <up-radio
            v-for="item in currencyTypeList" :key="item" :active-color="Color.primary" :label="item"
            :name="item"
          />
        </up-radio-group>
      </view>
    </view>
    <view class="rounded-md bg-white p-4">
      <view class="text-lg text-gray-500 font-bold">
        质量信息
      </view>
      <view class="space-y-4">
        <view v-for="(item, index) in formData?.standardList" :key="index">
          <view class="flex items-center justify-between py-2">
            <view class="font-bold">
              产品执行标准&nbsp;{{ index + 1 }}
            </view>
            <up-icon
              v-if="index !== 0" name="minus-circle" :color="Color.gray" class="shrink-0 pl-2"
              @click="handleMinusStandard(index)"
            />
          </view>
          <view class="f-form-item">
            <view class="f-label" @click="showTip('标准类型', '允许自行补充其他执行标准。')">
              <text class="pr-1">
                标准类型
              </text>
              <up-icon name="question-circle" :color="Color.gray" class="shrink-0" />
            </view>
            <view class="o-form-underline flex flex-1 items-center justify-between">
              <input
                v-model="item.executeStandard" class="flex-1" placeholder="请选择或填写标准类型"
                placeholder-style="color: #9ca3af;"
              >
              <up-icon
                name="order" :color="Color.gray" class="shrink-0 pl-2"
                @click="showExecuteStandardPicker(index)"
              />
            </view>
          </view>
          <view class="f-form-item">
            <view class="f-label">
              <text class="pr-1">
                标准号
              </text>
            </view>
            <view class="o-form-underline flex-1">
              <input v-model="item.standardNumber" placeholder="请输入标准号（选填）" placeholder-style="color: #9ca3af;">
            </view>
          </view>
          <view class="f-form-item">
            <view class="f-label">
              <text class="pr-1">
                年份
              </text>
            </view>
            <view class="o-form-underline flex-1">
              <input v-model="item.executeYear" placeholder="例如：2020" type="number" placeholder-style="color: #9ca3af;">
            </view>
          </view>
        </view>
        <view class="my-3 center space-x-2" @click="handleAddStandard">
          <up-icon name="plus-circle" :color="Color.primary" />
          <text class="text-primary">继续添加</text>
        </view>
      </view>
    </view>
    <!-- 类别外信息 -->
    <view v-if="attributeListForm.length > 0" class="rounded-md bg-white p-4 space-y-4">
      <view class="text-lg text-gray-500 font-bold">
        品类属性信息
      </view>
      <view
        v-for="(item, index) in attributeListForm" :id="`attribute${index}`" :key="item.attributeId"
        class="f-form-item"
      >
        <view class="f-label">
          <text class="pr-1" :class="item.isRequired && 'o-form-require'">
            {{ item.attributeCntitle }}
          </text>
        </view>
        <view
          v-if="item.attributeValueList.length > 0 && item.attributeValueList[0].attributeValue === '' && formData.attribueList[index]"
          class="o-form-underline flex flex-1 items-center"
        >
          <input
            v-model="formData.attribueList[index].attributeValue" type="number"
            placeholder-style="color: #9ca3af;"
          >
        </view>
        <view
          v-else-if="formData.attribueList[index]" class="o-form-underline flex flex-1 items-center"
          @click="showAttributePicker(item, index)"
        >
          <view class="flex-1 pr-2">
            {{ formData.attribueList[index].attributeValue || '' }}
          </view>
          <up-icon name="arrow-right" :color="Color.gray" class="shrink-0" />
        </view>
      </view>
    </view>
    <view
      :class="!isLoading ? 'o-bg-primary o-shadow-blue' : 'o-bg-primary-disable'"
      class="o-bg-primary o-shadow-blue mt-6 flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold"
      @click="handleSubmit"
    >
      提交通报
    </view>
    <view class="o-pb" />
    <ShowFormTip />
    <up-picker
      :show="isShowNetContentUnitPicker" :default-index="netContentUnitPickerDefaultIndex"
      :columns="netContentUnitPickerColumns" :close-on-click-overlay="true" @cancel="isShowNetContentUnitPicker = false"
      @close="isShowNetContentUnitPicker = false" @confirm="netContentUnitPickerConfirm"
    />
    <up-datetime-picker
      v-model="formData.marketDate" :show="isShowDatePicker" mode="date"
      :close-on-click-overlay="true" @close="saveDate" @confirm="saveDate" @cancel="saveDate"
    />
    <up-picker
      :show="isShowExecuteStandardPicker" :columns="executeStandardPickerColumns"
      :default-index="executeStandardPickerDefaultIndex" :close-on-click-overlay="true"
      @cancel="isShowExecuteStandardPicker = false" @close="isShowExecuteStandardPicker = false"
      @confirm="executeStandardConfirm"
    />
    <up-picker
      :show="isShowAttributePicker" :columns="attributePickerColumns" :close-on-click-overlay="true"
      :default-index="attributePickerDefaultIndex" @cancel="isShowAttributePicker = false"
      @close="isShowAttributePicker = false" @confirm="attributePickerConfirm"
    />
    <up-modal
      :show="isShowKeyModal" :show-cancel-button="true" title="条码成员身份校验" width="650rpx" confirm-text="提交"
      cancel-text="保存产品" @confirm="savePasswordAndReport" @cancel="justSaveProduct"
    >
      <view>
        <view class="f-form-item">
          <view class="f-label">
            <text class="o-form-require pr-1">
              条码卡号
            </text>
          </view>
          <view class="o-form-underline flex-1">
            <input v-model.trim="barCodeCardNum">
          </view>
        </view>
        <view class="f-form-item">
          <view class="f-label">
            <text class="o-form-require pr-1">
              条码卡号
            </text>
          </view>
          <view class="o-form-underline flex-1">
            <input v-model.trim="barCodeCardPassword">
          </view>
        </view>
      </view>
    </up-modal>
  </view>
</template>

<style lang="scss" scoped>
.f-form-item {
  @apply flex items-center justify-between space-x-2 w-full shrink-0;
}

.f-label {
  @apply text-gray flex items-center;
  min-width: 200rpx;
}

.upload-slot {
  width: 80px;
  height: 80px;
  border: 1px dashed #c0c4cc;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-slot:active {
  background-color: #f0f0f0;
}
</style>
